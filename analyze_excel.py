import pandas as pd
import openpyxl
import numpy as np

def analyze_excel_file():
    file_path = 'Depreciation Workings (Actual work to be done).xlsx'
    
    # Load workbook to see sheets
    wb = openpyxl.load_workbook(file_path)
    print('Available sheets:', wb.sheetnames)
    
    # Analyze each sheet
    for sheet_name in wb.sheetnames:
        print(f"\n=== SHEET: {sheet_name} ===")
        
        # Read the sheet
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        print(f"Shape: {df.shape}")
        
        # Show first 15 rows
        print("First 15 rows:")
        for i in range(min(15, len(df))):
            row_data = []
            for j in range(min(10, len(df.columns))):  # Show first 10 columns
                cell_value = df.iloc[i, j]
                if pd.isna(cell_value):
                    row_data.append("NaN")
                else:
                    row_data.append(str(cell_value)[:20])  # Truncate long values
            print(f"Row {i}: {row_data}")
        
        print("\n" + "="*50)

if __name__ == "__main__":
    analyze_excel_file()
