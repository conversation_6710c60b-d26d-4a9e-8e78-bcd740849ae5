import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import re

def final_clean():
    """Final cleaning with focus on key sheets"""
    input_file = 'Depreciation Workings (Actual work to be done).xlsx'
    output_file = 'FINAL_Cleaned_Depreciation_Schedule.xlsx'
    
    print("Starting final cleaning process...")
    
    try:
        # Load workbook
        wb = openpyxl.load_workbook(input_file)
        print(f"Loaded workbook with {len(wb.sheetnames)} sheets")
        
        # Create new workbook
        new_wb = openpyxl.Workbook()
        new_wb.remove(new_wb.active)
        
        # Define styles
        title_font = Font(name='<PERSON><PERSON>ri', size=16, bold=True, color='FFFFFF')
        title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
        
        header_font = Font(name='Calibri', size=12, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        
        subheader_font = Font(name='Calibri', size=11, bold=True)
        subheader_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
        
        normal_font = Font(name='Calibri', size=10)
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Process key sheets (first 20 sheets to avoid timeout)
        key_sheets = wb.sheetnames[:20]
        
        for i, sheet_name in enumerate(key_sheets):
            print(f"Processing {i+1}/{len(key_sheets)}: {sheet_name}")
            
            try:
                # Read sheet data
                df = pd.read_excel(input_file, sheet_name=sheet_name, header=None)
                
                # Skip if sheet is too small
                if df.shape[0] < 5:
                    continue
                
                # Create new sheet
                clean_name = sheet_name.replace('/', '_').replace('\\', '_')[:31]
                new_sheet = new_wb.create_sheet(title=clean_name)
                
                # Extract assets
                assets = extract_key_assets(df)
                
                if assets:
                    # Build the sheet
                    build_final_sheet(new_sheet, sheet_name, assets, 
                                    title_font, title_fill, header_font, header_fill,
                                    subheader_font, subheader_fill, normal_font, border)
                    print(f"  ✓ Added {len(assets)} assets")
                else:
                    print(f"  - No assets found")
                
            except Exception as e:
                print(f"  ✗ Error: {str(e)}")
                continue
        
        # Save the file
        new_wb.save(output_file)
        print(f"\n✓ SUCCESS: Cleaned file saved as: {output_file}")
        print(f"✓ Total sheets processed: {len(new_wb.sheetnames)}")
        return output_file
        
    except Exception as e:
        print(f"✗ ERROR: {str(e)}")
        return None

def extract_key_assets(df):
    """Extract key asset information"""
    assets = []
    
    # Key asset indicators
    asset_patterns = [
        r'building', r'computer', r'furniture', r'vehicle', r'land', r'equipment',
        r'bell', r'fan', r'refrigerator', r'washing', r'cctv', r'camera', 
        r'inverter', r'solar', r'vessels', r'utensils', r'honda', r'car',
        r'system', r'tank', r'electrical', r'generator', r'pump', r'machinery'
    ]
    
    for idx, row in df.iterrows():
        if idx > 100:  # Limit search to first 100 rows
            break
            
        row_values = [str(cell) if pd.notna(cell) else "" for cell in row]
        
        if not row_values:
            continue
            
        first_col = str(row_values[0]).strip()
        
        # Clean the asset name
        first_col = re.sub(r'[\r\n]+', ' ', first_col)
        first_col = re.sub(r'\s+', ' ', first_col)
        
        # Check if this is an asset
        if (first_col and 
            len(first_col) > 3 and
            first_col.lower() not in ['nan', 'particulars', 'total', 'operations department'] and
            not first_col.startswith('__EMPTY') and
            any(re.search(pattern, first_col.lower()) for pattern in asset_patterns)):
            
            # Look for numeric values in the row
            numbers = []
            for val in row_values[1:]:
                try:
                    if val and str(val).strip() and str(val) != 'nan':
                        clean_val = str(val).replace(',', '').replace('`', '').strip()
                        if clean_val and clean_val != '.':
                            num = float(clean_val)
                            if num > 0:
                                numbers.append(num)
                            else:
                                numbers.append("")
                        else:
                            numbers.append("")
                    else:
                        numbers.append("")
                except:
                    numbers.append("")
            
            # Only include if we have meaningful numeric data
            if any(isinstance(n, (int, float)) and n > 0 for n in numbers):
                # Ensure we have 7 columns for the asset data
                while len(numbers) < 7:
                    numbers.append("")
                
                asset = {
                    'name': first_col,
                    'values': numbers[:7]  # Take first 7 numeric values
                }
                assets.append(asset)
    
    # Remove duplicates and limit
    unique_assets = []
    seen = set()
    for asset in assets:
        name_key = asset['name'].lower().strip()
        if name_key not in seen and len(unique_assets) < 25:
            seen.add(name_key)
            unique_assets.append(asset)
    
    return unique_assets

def build_final_sheet(sheet, sheet_name, assets, title_font, title_fill, 
                     header_font, header_fill, subheader_font, subheader_fill, 
                     normal_font, border):
    """Build the final formatted sheet"""
    
    # Organization header
    sheet.merge_cells('A1:H1')
    sheet['A1'] = "SANTHIGIRI ASHRAM"
    sheet['A1'].font = title_font
    sheet['A1'].fill = title_fill
    sheet['A1'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Branch/Department name
    sheet.merge_cells('A2:H2')
    sheet['A2'] = f"{sheet_name.upper()} BRANCH"
    sheet['A2'].font = header_font
    sheet['A2'].fill = header_fill
    sheet['A2'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Period
    sheet.merge_cells('A3:H3')
    sheet['A3'] = "DEPRECIATION SCHEDULE FOR THE YEAR ENDED 31-03-2024"
    sheet['A3'].font = subheader_font
    sheet['A3'].fill = subheader_fill
    sheet['A3'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Empty row
    sheet.row_dimensions[4].height = 10
    
    # Table header
    sheet.merge_cells('A5:H5')
    sheet['A5'] = "SCHEDULE OF FIXED ASSETS"
    sheet['A5'].font = subheader_font
    sheet['A5'].fill = subheader_fill
    sheet['A5'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Column headers
    headers = [
        "Asset Description",
        "WDV Opening\n(01-04-2023)", 
        "Additions\nDuring Year",
        "Deletions\nDuring Year",
        "Gross Block\n(Closing)",
        "Depreciation\nRate (%)",
        "Depreciation\nfor Year",
        "WDV Closing\n(31-03-2024)"
    ]
    
    for col, header in enumerate(headers, 1):
        cell = sheet.cell(row=7, column=col, value=header)
        cell.font = Font(name='Calibri', size=10, bold=True)
        cell.fill = subheader_fill
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        cell.border = border
    
    # Asset data
    row_num = 8
    total_values = [0] * 7
    
    for asset in assets:
        # Asset name
        name_cell = sheet.cell(row=row_num, column=1, value=asset['name'])
        name_cell.font = normal_font
        name_cell.alignment = Alignment(horizontal='left', vertical='center')
        name_cell.border = border
        
        # Asset values
        for col, value in enumerate(asset['values'], 2):
            cell = sheet.cell(row=row_num, column=col, value=value)
            cell.font = normal_font
            cell.border = border
            
            if value and isinstance(value, (int, float)) and value > 0:
                cell.alignment = Alignment(horizontal='right', vertical='center')
                cell.number_format = '₹#,##0.00'
                # Add to totals (skip rate column)
                if col != 6:  # Skip rate column for totals
                    total_values[col-2] += value
            else:
                cell.alignment = Alignment(horizontal='center', vertical='center')
        
        row_num += 1
    
    # Add total row
    if assets:
        total_cell = sheet.cell(row=row_num, column=1, value="TOTAL")
        total_cell.font = Font(name='Calibri', size=10, bold=True)
        total_cell.fill = PatternFill(start_color='E7E6E6', end_color='E7E6E6', fill_type='solid')
        total_cell.alignment = Alignment(horizontal='center', vertical='center')
        total_cell.border = border
        
        for col, total in enumerate(total_values, 2):
            if col != 6:  # Skip rate column
                cell = sheet.cell(row=row_num, column=col, value=total if total > 0 else "")
                cell.font = Font(name='Calibri', size=10, bold=True)
                cell.fill = PatternFill(start_color='E7E6E6', end_color='E7E6E6', fill_type='solid')
                cell.alignment = Alignment(horizontal='right', vertical='center')
                cell.number_format = '₹#,##0.00'
                cell.border = border
            else:
                cell = sheet.cell(row=row_num, column=col, value="")
                cell.border = border
    
    # Set column widths
    column_widths = [30, 15, 12, 12, 15, 10, 15, 15]
    for col, width in enumerate(column_widths, 1):
        sheet.column_dimensions[chr(64 + col)].width = width
    
    # Set row heights
    sheet.row_dimensions[7].height = 30  # Header row

if __name__ == "__main__":
    result = final_clean()
    if result:
        print(f"\n🎉 COMPLETED! Your cleaned file is ready: {result}")
    else:
        print("\n❌ Process failed. Please check the error messages above.")
