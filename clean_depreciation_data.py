import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import numpy as np

def clean_and_format_excel():
    """
    Clean and format the depreciation Excel file to make it presentable for higher authorities
    """
    input_file = 'Depreciation Workings (Actual work to be done).xlsx'
    output_file = 'Cleaned_Depreciation_Schedule.xlsx'

    try:
        # Load the workbook
        print("Loading workbook...")
        wb = openpyxl.load_workbook(input_file)

        # Create a new workbook for cleaned data
        new_wb = openpyxl.Workbook()
        new_wb.remove(new_wb.active)  # Remove default sheet

        # Define styles
        header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        subheader_font = Font(name='Arial', size=11, bold=True)
        subheader_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
        normal_font = Font(name='Arial', size=10)

        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        center_alignment = Alignment(horizontal='center', vertical='center')
        right_alignment = Alignment(horizontal='right', vertical='center')
        left_alignment = Alignment(horizontal='left', vertical='center')

        print(f"Processing {len(wb.sheetnames)} sheets...")

        # Process first few sheets as a test
        sheets_to_process = wb.sheetnames[:5]  # Process first 5 sheets for testing

        for sheet_name in sheets_to_process:
            print(f"Processing sheet: {sheet_name}")

            try:
                # Read the sheet
                df = pd.read_excel(input_file, sheet_name=sheet_name, header=None)

                # Create new sheet
                new_sheet = new_wb.create_sheet(title=sheet_name)

                # Clean and structure the data
                cleaned_data = clean_sheet_data(df, sheet_name)

                if cleaned_data is not None and not cleaned_data.empty:
                    # Write cleaned data to new sheet
                    write_formatted_data(new_sheet, cleaned_data, header_font, header_fill,
                                       subheader_font, subheader_fill, normal_font, border,
                                       center_alignment, right_alignment, left_alignment)

                    print(f"Successfully processed {sheet_name}")
                else:
                    print(f"No data found in {sheet_name}")

            except Exception as e:
                print(f"Error processing sheet {sheet_name}: {str(e)}")
                continue

        # Save the cleaned workbook
        new_wb.save(output_file)
        print(f"Cleaned file saved as: {output_file}")
        return output_file

    except Exception as e:
        print(f"Error in main function: {str(e)}")
        return None

def write_formatted_data(sheet, data, header_font, header_fill, subheader_font,
                        subheader_fill, normal_font, border, center_align, right_align, left_align):
    """Write formatted data to sheet"""
    for r_idx, row in enumerate(dataframe_to_rows(data, index=False, header=False), 1):
        for c_idx, value in enumerate(row, 1):
            cell = sheet.cell(row=r_idx, column=c_idx, value=value)

            # Apply formatting based on content and position
            if r_idx <= 3:  # Title rows
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_align
            elif "Particulars" in str(value) or "WDV" in str(value):  # Header row
                cell.font = subheader_font
                cell.fill = subheader_fill
                cell.alignment = center_align
            else:
                cell.font = normal_font
                if isinstance(value, (int, float)) and not pd.isna(value) and value != 0:
                    cell.alignment = right_align
                    cell.number_format = '#,##0.00'
                else:
                    cell.alignment = left_align

            cell.border = border

    # Auto-adjust column widths
    for column in sheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 30)
        sheet.column_dimensions[column_letter].width = adjusted_width

def clean_sheet_data(df, sheet_name):
    """
    Clean individual sheet data
    """
    if df.empty:
        return None

    # Remove completely empty rows and columns
    df = df.dropna(how='all').dropna(axis=1, how='all')

    if df.empty:
        return None

    # Create a structured output
    cleaned_data = []

    # Add sheet title
    cleaned_data.append([f"SANTHIGIRI ASHRAM - {sheet_name.upper()}", "", "", "", "", "", "", ""])
    cleaned_data.append(["DEPRECIATION SCHEDULE FOR THE YEAR ENDED 31-03-2024", "", "", "", "", "", "", ""])
    cleaned_data.append(["", "", "", "", "", "", "", ""])

    # Look for Fixed Assets data in the sheet
    fa_data = extract_fixed_assets_from_sheet(df)
    if fa_data:
        cleaned_data.extend(fa_data)
    else:
        # If no specific FA data found, create a basic structure
        cleaned_data.append(["SCHEDULE OF FIXED ASSETS", "", "", "", "", "", "", ""])
        cleaned_data.append(["", "", "", "", "", "", "", ""])

        # Standard headers
        headers = [
            "Particulars",
            "WDV as on 1.4.2023",
            "Additions",
            "Deletions",
            "Total",
            "Rate of Deprn (%)",
            "Deprn for the year",
            "WDV as on 31-03-2024"
        ]
        cleaned_data.append(headers)

        # Try to extract any asset data from the sheet
        asset_data = extract_asset_items(df)
        if asset_data:
            cleaned_data.extend(asset_data)

    # Convert to DataFrame
    if cleaned_data:
        max_cols = max(len(row) for row in cleaned_data)
        for row in cleaned_data:
            while len(row) < max_cols:
                row.append("")

        columns = [f"Column_{i+1}" for i in range(max_cols)]
        result_df = pd.DataFrame(cleaned_data, columns=columns)
        return result_df

    return None

def extract_fixed_assets_from_sheet(df):
    """
    Extract fixed assets data from the sheet by looking for specific patterns
    """
    fa_data = []

    # Add Fixed Assets header
    fa_data.append(["SCHEDULE OF FIXED ASSETS", "", "", "", "", "", "", ""])
    fa_data.append(["", "", "", "", "", "", "", ""])

    # Standard Fixed Assets table headers
    headers = [
        "Particulars",
        "WDV as on 1.4.2023",
        "Additions",
        "Deletions",
        "Total",
        "Rate of Deprn (%)",
        "Deprn for the year",
        "WDV as on 31-03-2024"
    ]
    fa_data.append(headers)

    # Look for asset data in the entire sheet
    asset_items = []

    for idx, row in df.iterrows():
        row_values = [str(cell) if pd.notna(cell) else "" for cell in row]

        # Skip empty rows
        if all(val == "" or val == "nan" for val in row_values):
            continue

        first_col = row_values[0] if row_values else ""

        # Look for common asset names
        asset_keywords = ['building', 'computer', 'furniture', 'vehicle', 'land', 'equipment',
                         'bell', 'fan', 'refrigerator', 'washing', 'cctv', 'camera', 'inverter',
                         'solar', 'vessels', 'utensils', 'honda', 'car', 'system', 'tank']

        if (first_col and
            first_col.lower() not in ['nan', 'particulars', 'total', 'grand total', 'operations department'] and
            not first_col.startswith('__EMPTY') and
            any(keyword in first_col.lower() for keyword in asset_keywords)):

            # Extract numeric values from the row
            asset_row = [first_col.strip()]  # Asset name

            # Look for numeric values in the row
            numeric_values = []
            for val in row_values[1:]:
                try:
                    if val and val != "nan" and val.strip():
                        # Try to convert to float
                        numeric_val = float(str(val).replace(',', ''))
                        numeric_values.append(numeric_val if numeric_val != 0 else "")
                    else:
                        numeric_values.append("")
                except:
                    numeric_values.append("")

            # Ensure we have the right number of columns (7 numeric columns)
            while len(numeric_values) < 7:
                numeric_values.append("")

            asset_row.extend(numeric_values[:7])
            asset_items.append(asset_row)

    # Add asset items to fa_data
    if asset_items:
        fa_data.extend(asset_items)

        # Add total row
        total_row = ["TOTAL", "", "", "", "", "", "", ""]
        fa_data.append(total_row)

    return fa_data if asset_items else None

def extract_asset_items(df):
    """
    Extract individual asset items from the dataframe
    """
    asset_items = []

    for idx, row in df.iterrows():
        row_values = [str(cell) if pd.notna(cell) else "" for cell in row]

        if not row_values or all(val == "" or val == "nan" for val in row_values):
            continue

        first_col = row_values[0] if row_values else ""

        # Simple asset detection
        if (first_col and
            len(first_col) > 2 and
            first_col.lower() not in ['nan', 'particulars', 'total'] and
            not first_col.startswith('__EMPTY')):

            # Create asset row with 8 columns
            asset_row = [first_col]

            # Add 7 empty columns for the numeric data
            for i in range(7):
                asset_row.append("")

            asset_items.append(asset_row)

    return asset_items[:10]  # Limit to first 10 items

if __name__ == "__main__":
    clean_and_format_excel()
