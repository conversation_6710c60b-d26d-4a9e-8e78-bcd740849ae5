import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import re

def comprehensive_clean():
    """Comprehensive cleaning of all sheets"""
    input_file = 'Depreciation Workings (Actual work to be done).xlsx'
    output_file = 'Final_Cleaned_Depreciation_Schedule.xlsx'
    
    print("Starting comprehensive cleaning process...")
    
    try:
        # Load workbook
        wb = openpyxl.load_workbook(input_file)
        print(f"Loaded workbook with {len(wb.sheetnames)} sheets")
        
        # Create new workbook
        new_wb = openpyxl.Workbook()
        new_wb.remove(new_wb.active)
        
        # Define styles
        title_font = Font(name='Arial', size=14, bold=True, color='FFFFFF')
        title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
        
        header_font = Font(name='Arial', size=11, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        
        subheader_font = Font(name='Arial', size=10, bold=True)
        subheader_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
        
        normal_font = Font(name='Arial', size=9)
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Process all sheets
        for i, sheet_name in enumerate(wb.sheetnames):
            print(f"Processing sheet {i+1}/{len(wb.sheetnames)}: {sheet_name}")
            
            try:
                # Read sheet data
                df = pd.read_excel(input_file, sheet_name=sheet_name, header=None)
                
                # Create new sheet
                new_sheet = new_wb.create_sheet(title=sheet_name[:31])  # Excel sheet name limit
                
                # Extract and clean data
                assets = extract_assets_from_sheet(df)
                
                # Build the cleaned sheet
                build_cleaned_sheet(new_sheet, sheet_name, assets, 
                                  title_font, title_fill, header_font, header_fill,
                                  subheader_font, subheader_fill, normal_font, border)
                
                print(f"  - Added {len(assets)} assets")
                
            except Exception as e:
                print(f"  - Error processing {sheet_name}: {str(e)}")
                continue
        
        # Save the file
        new_wb.save(output_file)
        print(f"\nCleaned file saved as: {output_file}")
        print(f"Total sheets processed: {len(new_wb.sheetnames)}")
        return True
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def extract_assets_from_sheet(df):
    """Extract asset information from a sheet"""
    assets = []
    
    # Asset keywords to look for
    asset_keywords = [
        'building', 'computer', 'furniture', 'vehicle', 'land', 'equipment', 
        'bell', 'fan', 'refrigerator', 'washing', 'cctv', 'camera', 'inverter',
        'solar', 'vessels', 'utensils', 'honda', 'car', 'system', 'tank',
        'electrical', 'generator', 'pump', 'machinery', 'tools', 'office',
        'air conditioner', 'ac', 'printer', 'scanner', 'phone', 'mobile'
    ]
    
    for idx, row in df.iterrows():
        row_values = [str(cell) if pd.notna(cell) else "" for cell in row]
        
        if not row_values or all(val == "" or val == "nan" for val in row_values):
            continue
            
        first_col = row_values[0] if row_values else ""
        
        # Clean the first column
        first_col = re.sub(r'[\r\n]+', ' ', first_col).strip()
        
        # Check if this looks like an asset
        if (first_col and 
            len(first_col) > 2 and
            first_col.lower() not in ['nan', 'particulars', 'total', 'grand total', 
                                    'operations department', 'expenditure', 'income',
                                    'to', 'by', 'amount'] and
            not first_col.startswith('__EMPTY') and
            not first_col.startswith('"') and
            (any(keyword in first_col.lower() for keyword in asset_keywords) or
             len(first_col) > 5)):  # Include longer descriptive names
            
            # Extract numeric values
            numeric_values = []
            for val in row_values[1:]:
                try:
                    if val and val != "nan" and str(val).strip():
                        # Clean and convert to number
                        clean_val = str(val).replace(',', '').replace('`', '').strip()
                        if clean_val and clean_val != '.':
                            numeric_val = float(clean_val)
                            numeric_values.append(numeric_val if numeric_val > 0 else "")
                        else:
                            numeric_values.append("")
                    else:
                        numeric_values.append("")
                except:
                    numeric_values.append("")
            
            # Only include if we have at least one numeric value
            if any(isinstance(val, (int, float)) and val > 0 for val in numeric_values):
                asset_data = {
                    'name': first_col,
                    'wdv_opening': numeric_values[0] if len(numeric_values) > 0 else "",
                    'additions': numeric_values[1] if len(numeric_values) > 1 else "",
                    'deletions': numeric_values[2] if len(numeric_values) > 2 else "",
                    'total': numeric_values[3] if len(numeric_values) > 3 else "",
                    'rate': numeric_values[4] if len(numeric_values) > 4 else "",
                    'depreciation': numeric_values[5] if len(numeric_values) > 5 else "",
                    'wdv_closing': numeric_values[6] if len(numeric_values) > 6 else ""
                }
                assets.append(asset_data)
    
    # Remove duplicates and limit to reasonable number
    seen_names = set()
    unique_assets = []
    for asset in assets:
        if asset['name'].lower() not in seen_names and len(unique_assets) < 30:
            seen_names.add(asset['name'].lower())
            unique_assets.append(asset)
    
    return unique_assets

def build_cleaned_sheet(sheet, sheet_name, assets, title_font, title_fill, 
                       header_font, header_fill, subheader_font, subheader_fill, 
                       normal_font, border):
    """Build the cleaned sheet with proper formatting"""
    
    # Title section
    sheet.merge_cells('A1:H1')
    sheet['A1'] = f"SANTHIGIRI ASHRAM - {sheet_name.upper()}"
    sheet['A1'].font = title_font
    sheet['A1'].fill = title_fill
    sheet['A1'].alignment = Alignment(horizontal='center', vertical='center')
    
    sheet.merge_cells('A2:H2')
    sheet['A2'] = "DEPRECIATION SCHEDULE FOR THE YEAR ENDED 31-03-2024"
    sheet['A2'].font = header_font
    sheet['A2'].fill = header_fill
    sheet['A2'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Empty row
    sheet.row_dimensions[3].height = 15
    
    # Section header
    sheet.merge_cells('A4:H4')
    sheet['A4'] = "SCHEDULE OF FIXED ASSETS"
    sheet['A4'].font = subheader_font
    sheet['A4'].fill = subheader_fill
    sheet['A4'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Column headers
    headers = [
        "Particulars",
        "WDV as on 1.4.2023", 
        "Additions",
        "Deletions",
        "Total",
        "Rate of Deprn (%)",
        "Deprn for the year",
        "WDV as on 31-03-2024"
    ]
    
    for col, header in enumerate(headers, 1):
        cell = sheet.cell(row=6, column=col, value=header)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = border
    
    # Asset data
    row_num = 7
    for asset in assets:
        sheet.cell(row=row_num, column=1, value=asset['name']).font = normal_font
        sheet.cell(row=row_num, column=2, value=asset['wdv_opening']).font = normal_font
        sheet.cell(row=row_num, column=3, value=asset['additions']).font = normal_font
        sheet.cell(row=row_num, column=4, value=asset['deletions']).font = normal_font
        sheet.cell(row=row_num, column=5, value=asset['total']).font = normal_font
        sheet.cell(row=row_num, column=6, value=asset['rate']).font = normal_font
        sheet.cell(row=row_num, column=7, value=asset['depreciation']).font = normal_font
        sheet.cell(row=row_num, column=8, value=asset['wdv_closing']).font = normal_font
        
        # Apply borders and formatting
        for col in range(1, 9):
            cell = sheet.cell(row=row_num, column=col)
            cell.border = border
            if col > 1 and cell.value and isinstance(cell.value, (int, float)):
                cell.alignment = Alignment(horizontal='right', vertical='center')
                cell.number_format = '#,##0.00'
            else:
                cell.alignment = Alignment(horizontal='left', vertical='center')
        
        row_num += 1
    
    # Set column widths
    column_widths = [25, 15, 12, 12, 12, 10, 15, 15]
    for col, width in enumerate(column_widths, 1):
        sheet.column_dimensions[chr(64 + col)].width = width

if __name__ == "__main__":
    comprehensive_clean()
