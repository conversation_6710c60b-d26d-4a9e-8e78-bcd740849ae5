import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill

def simple_clean():
    """Simple cleaning approach"""
    input_file = 'Depreciation Workings (Actual work to be done).xlsx'
    output_file = 'Cleaned_Depreciation_Schedule.xlsx'
    
    print("Starting simple cleaning process...")
    
    try:
        # Load workbook
        wb = openpyxl.load_workbook(input_file)
        print(f"Loaded workbook with {len(wb.sheetnames)} sheets")
        
        # Create new workbook
        new_wb = openpyxl.Workbook()
        new_wb.remove(new_wb.active)
        
        # Process first 3 sheets as test
        for i, sheet_name in enumerate(wb.sheetnames[:3]):
            print(f"Processing sheet {i+1}: {sheet_name}")
            
            # Read sheet data
            df = pd.read_excel(input_file, sheet_name=sheet_name, header=None)
            print(f"Sheet shape: {df.shape}")
            
            # Create new sheet
            new_sheet = new_wb.create_sheet(title=sheet_name)
            
            # Add title
            new_sheet['A1'] = f"SANTHIGIRI ASHRAM - {sheet_name.upper()}"
            new_sheet['A2'] = "DEPRECIATION SCHEDULE FOR THE YEAR ENDED 31-03-2024"
            new_sheet['A4'] = "SCHEDULE OF FIXED ASSETS"
            
            # Add headers
            headers = ["Particulars", "WDV as on 1.4.2023", "Additions", "Deletions", 
                      "Total", "Rate of Deprn (%)", "Deprn for the year", "WDV as on 31-03-2024"]
            
            for col, header in enumerate(headers, 1):
                new_sheet.cell(row=6, column=col, value=header)
            
            # Look for asset data
            row_num = 7
            asset_count = 0
            
            for idx, row in df.iterrows():
                if asset_count >= 20:  # Limit to 20 assets per sheet
                    break
                    
                row_values = [str(cell) if pd.notna(cell) else "" for cell in row]
                first_col = row_values[0] if row_values else ""
                
                # Simple asset detection
                asset_keywords = ['building', 'computer', 'furniture', 'vehicle', 'land', 'equipment', 
                                'bell', 'fan', 'refrigerator', 'washing', 'cctv', 'camera', 'inverter',
                                'solar', 'vessels', 'utensils', 'honda', 'car', 'system', 'tank']
                
                if (first_col and 
                    len(first_col) > 2 and
                    first_col.lower() not in ['nan', 'particulars', 'total', 'operations department'] and
                    any(keyword in first_col.lower() for keyword in asset_keywords)):
                    
                    # Add asset name
                    new_sheet.cell(row=row_num, column=1, value=first_col.strip())
                    
                    # Look for numeric values in the original row
                    col_num = 2
                    for val in row_values[1:]:
                        if col_num > 8:  # Don't exceed our header columns
                            break
                        try:
                            if val and val != "nan" and str(val).strip():
                                numeric_val = float(str(val).replace(',', ''))
                                if numeric_val > 0:
                                    new_sheet.cell(row=row_num, column=col_num, value=numeric_val)
                        except:
                            pass
                        col_num += 1
                    
                    row_num += 1
                    asset_count += 1
            
            print(f"Added {asset_count} assets to {sheet_name}")
            
            # Apply basic formatting
            apply_basic_formatting(new_sheet, row_num)
        
        # Save the file
        new_wb.save(output_file)
        print(f"Cleaned file saved as: {output_file}")
        return True
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def apply_basic_formatting(sheet, last_row):
    """Apply basic formatting to the sheet"""
    # Define styles
    header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    subheader_font = Font(name='Arial', size=11, bold=True)
    subheader_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
    normal_font = Font(name='Arial', size=10)
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Format title rows
    for row in [1, 2, 4]:
        for col in range(1, 9):
            cell = sheet.cell(row=row, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center', vertical='center')
    
    # Format header row
    for col in range(1, 9):
        cell = sheet.cell(row=6, column=col)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = border
    
    # Format data rows
    for row in range(7, last_row):
        for col in range(1, 9):
            cell = sheet.cell(row=row, column=col)
            cell.font = normal_font
            cell.border = border
            if col > 1 and cell.value:  # Numeric columns
                cell.alignment = Alignment(horizontal='right', vertical='center')
                cell.number_format = '#,##0.00'
            else:
                cell.alignment = Alignment(horizontal='left', vertical='center')
    
    # Auto-adjust column widths
    for column in sheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 25)
        sheet.column_dimensions[column_letter].width = adjusted_width

if __name__ == "__main__":
    simple_clean()
