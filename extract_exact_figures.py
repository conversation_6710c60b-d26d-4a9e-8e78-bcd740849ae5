import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import re

def extract_exact_figures():
    """Extract exact figures from the original depreciation file"""
    input_file = 'Depreciation Workings (Actual work to be done).xlsx'
    output_file = 'EXACT_Figures_Depreciation_Schedule.xlsx'
    
    print("Extracting exact figures from original file...")
    
    try:
        # Load workbook
        wb = openpyxl.load_workbook(input_file)
        print(f"Loaded workbook with {len(wb.sheetnames)} sheets")
        
        # Create new workbook
        new_wb = openpyxl.Workbook()
        new_wb.remove(new_wb.active)
        
        # Define styles
        title_font = Font(name='<PERSON><PERSON><PERSON>', size=14, bold=True, color='FFFFFF')
        title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
        
        header_font = Font(name='<PERSON><PERSON><PERSON>', size=11, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        
        subheader_font = Font(name='<PERSON><PERSON>ri', size=10, bold=True)
        subheader_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
        
        normal_font = Font(name='Calibri', size=9)
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Process first 15 sheets to start with
        sheets_to_process = wb.sheetnames[:15]
        
        for i, sheet_name in enumerate(sheets_to_process):
            print(f"Processing {i+1}/{len(sheets_to_process)}: {sheet_name}")
            
            try:
                # Read sheet data with all possible data
                df = pd.read_excel(input_file, sheet_name=sheet_name, header=None)
                
                # Skip very small sheets
                if df.shape[0] < 3 or df.shape[1] < 3:
                    print(f"  - Skipping {sheet_name} (too small)")
                    continue
                
                # Create new sheet
                clean_name = sheet_name.replace('/', '_').replace('\\', '_')[:31]
                new_sheet = new_wb.create_sheet(title=clean_name)
                
                # Extract detailed asset data with exact figures
                asset_data = extract_detailed_asset_data(df, sheet_name)
                
                if asset_data and len(asset_data) > 0:
                    # Build the sheet with exact figures
                    build_exact_figures_sheet(new_sheet, sheet_name, asset_data, 
                                            title_font, title_fill, header_font, header_fill,
                                            subheader_font, subheader_fill, normal_font, border)
                    print(f"  ✓ Added {len(asset_data)} assets with exact figures")
                else:
                    print(f"  - No asset data found in {sheet_name}")
                
            except Exception as e:
                print(f"  ✗ Error processing {sheet_name}: {str(e)}")
                continue
        
        # Save the file
        new_wb.save(output_file)
        print(f"\n✓ SUCCESS: File with exact figures saved as: {output_file}")
        print(f"✓ Total sheets processed: {len(new_wb.sheetnames)}")
        return output_file
        
    except Exception as e:
        print(f"✗ ERROR: {str(e)}")
        return None

def extract_detailed_asset_data(df, sheet_name):
    """Extract detailed asset data with exact figures"""
    assets = []
    
    print(f"  Analyzing {df.shape[0]} rows x {df.shape[1]} columns")
    
    # Look for the fixed assets section more carefully
    fa_start_row = None
    fa_header_row = None
    
    # Search for Fixed Assets section indicators
    for idx, row in df.iterrows():
        row_str = ' '.join([str(cell) for cell in row if pd.notna(cell)]).lower()
        
        # Look for fixed assets indicators
        if any(indicator in row_str for indicator in ['schedule of fixed', 'fixed asset', 'wdv as on', 'particulars']):
            if 'particulars' in row_str and any(term in row_str for term in ['wdv', 'addition', 'depreciation']):
                fa_header_row = idx
                fa_start_row = idx + 1
                print(f"  Found FA header at row {idx}")
                break
            elif 'schedule of fixed' in row_str or 'fixed asset' in row_str:
                fa_start_row = idx + 1
                print(f"  Found FA section at row {idx}")
    
    # If no clear header found, look for asset data patterns
    if fa_start_row is None:
        # Look for rows with asset-like names and numeric data
        for idx, row in df.iterrows():
            row_values = [str(cell) if pd.notna(cell) else "" for cell in row]
            first_col = row_values[0] if row_values else ""
            
            # Check if this looks like an asset row with numbers
            if (first_col and len(first_col) > 3 and
                any(keyword in first_col.lower() for keyword in ['building', 'computer', 'land', 'vehicle']) and
                any(is_number(val) for val in row_values[1:])):
                fa_start_row = idx
                print(f"  Found asset data starting at row {idx}")
                break
    
    if fa_start_row is not None:
        # Extract asset data starting from the identified row
        for idx in range(fa_start_row, min(fa_start_row + 50, len(df))):
            if idx >= len(df):
                break
                
            row = df.iloc[idx]
            row_values = [cell for cell in row]
            
            # Convert to strings for processing
            str_values = [str(cell) if pd.notna(cell) else "" for cell in row_values]
            
            # Skip completely empty rows
            if all(val == "" or val == "nan" for val in str_values):
                continue
            
            first_col = str_values[0].strip() if str_values else ""
            
            # Clean the asset name
            first_col = re.sub(r'[\r\n]+', ' ', first_col)
            first_col = re.sub(r'\s+', ' ', first_col)
            
            # Check if this is a valid asset row
            if is_valid_asset_row(first_col, str_values):
                # Extract all numeric values from the row
                numeric_data = extract_numeric_values(row_values)
                
                if numeric_data and any(val > 0 for val in numeric_data if isinstance(val, (int, float))):
                    asset_info = {
                        'name': first_col,
                        'raw_data': numeric_data,
                        'row_index': idx
                    }
                    assets.append(asset_info)
                    print(f"    Found asset: {first_col[:30]}... with {len([x for x in numeric_data if isinstance(x, (int, float)) and x > 0])} numeric values")
    
    # Remove duplicates and clean up
    unique_assets = []
    seen_names = set()
    
    for asset in assets:
        name_key = asset['name'].lower().strip()
        if name_key not in seen_names and len(unique_assets) < 30:
            seen_names.add(name_key)
            unique_assets.append(asset)
    
    return unique_assets

def is_valid_asset_row(first_col, str_values):
    """Check if this row represents a valid asset"""
    if not first_col or len(first_col) < 3:
        return False
    
    # Skip obvious non-asset rows
    skip_terms = ['nan', 'particulars', 'total', 'grand total', 'operations department', 
                  'expenditure', 'income', 'to', 'by', 'amount', '__empty', 'trial balance']
    
    if any(term in first_col.lower() for term in skip_terms):
        return False
    
    # Must have at least one numeric value in the row
    has_numbers = any(is_number(val) for val in str_values[1:])
    
    return has_numbers

def is_number(val):
    """Check if a value can be converted to a number"""
    try:
        if val and str(val).strip() and str(val) != 'nan':
            clean_val = str(val).replace(',', '').replace('`', '').replace('.', '', 1).strip()
            if clean_val and clean_val.replace('-', '').replace('+', '').isdigit():
                return True
            float(str(val).replace(',', ''))
            return True
    except:
        pass
    return False

def extract_numeric_values(row_values):
    """Extract numeric values from a row, preserving exact figures"""
    numeric_data = []
    
    for val in row_values[1:]:  # Skip first column (asset name)
        try:
            if pd.notna(val) and str(val).strip() and str(val) != 'nan':
                # Clean the value
                clean_val = str(val).replace(',', '').replace('`', '').strip()
                
                if clean_val and clean_val != '.' and clean_val != '0':
                    try:
                        num_val = float(clean_val)
                        numeric_data.append(num_val)
                    except:
                        numeric_data.append("")
                else:
                    numeric_data.append("")
            else:
                numeric_data.append("")
        except:
            numeric_data.append("")
    
    return numeric_data

def build_exact_figures_sheet(sheet, sheet_name, asset_data, title_font, title_fill, 
                             header_font, header_fill, subheader_font, subheader_fill, 
                             normal_font, border):
    """Build sheet with exact figures"""
    
    # Title section
    sheet.merge_cells('A1:H1')
    sheet['A1'] = "SANTHIGIRI ASHRAM"
    sheet['A1'].font = title_font
    sheet['A1'].fill = title_fill
    sheet['A1'].alignment = Alignment(horizontal='center', vertical='center')
    
    sheet.merge_cells('A2:H2')
    sheet['A2'] = f"{sheet_name.upper()} - DEPRECIATION SCHEDULE"
    sheet['A2'].font = header_font
    sheet['A2'].fill = header_fill
    sheet['A2'].alignment = Alignment(horizontal='center', vertical='center')
    
    sheet.merge_cells('A3:H3')
    sheet['A3'] = "FOR THE YEAR ENDED 31-03-2024"
    sheet['A3'].font = subheader_font
    sheet['A3'].fill = subheader_fill
    sheet['A3'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Empty row
    sheet.row_dimensions[4].height = 10
    
    # Table header
    sheet.merge_cells('A5:H5')
    sheet['A5'] = "SCHEDULE OF FIXED ASSETS"
    sheet['A5'].font = subheader_font
    sheet['A5'].fill = subheader_fill
    sheet['A5'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Column headers
    headers = [
        "Asset Description",
        "WDV Opening\n(01-04-2023)", 
        "Additions\nDuring Year",
        "Deletions\nDuring Year",
        "Gross Block\n(Closing)",
        "Depreciation\nRate (%)",
        "Depreciation\nfor Year",
        "WDV Closing\n(31-03-2024)"
    ]
    
    for col, header in enumerate(headers, 1):
        cell = sheet.cell(row=7, column=col, value=header)
        cell.font = Font(name='Calibri', size=9, bold=True)
        cell.fill = subheader_fill
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        cell.border = border
    
    # Asset data with exact figures
    row_num = 8
    total_values = [0] * 7
    
    for asset in asset_data:
        # Asset name
        name_cell = sheet.cell(row=row_num, column=1, value=asset['name'])
        name_cell.font = normal_font
        name_cell.alignment = Alignment(horizontal='left', vertical='center')
        name_cell.border = border
        
        # Asset values - use exact figures from raw data
        raw_data = asset['raw_data']
        for col in range(2, 9):  # Columns B through H
            data_index = col - 2
            if data_index < len(raw_data):
                value = raw_data[data_index]
                cell = sheet.cell(row=row_num, column=col, value=value)
                cell.font = normal_font
                cell.border = border
                
                if value and isinstance(value, (int, float)) and value > 0:
                    cell.alignment = Alignment(horizontal='right', vertical='center')
                    cell.number_format = '₹#,##0.00'
                    # Add to totals (skip rate column)
                    if col != 6:  # Skip rate column for totals
                        total_values[data_index] += value
                else:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
            else:
                cell = sheet.cell(row=row_num, column=col, value="")
                cell.border = border
        
        row_num += 1
    
    # Add total row
    if asset_data:
        total_cell = sheet.cell(row=row_num, column=1, value="TOTAL")
        total_cell.font = Font(name='Calibri', size=10, bold=True)
        total_cell.fill = PatternFill(start_color='E7E6E6', end_color='E7E6E6', fill_type='solid')
        total_cell.alignment = Alignment(horizontal='center', vertical='center')
        total_cell.border = border
        
        for col in range(2, 9):
            data_index = col - 2
            if col != 6 and data_index < len(total_values):  # Skip rate column
                total_val = total_values[data_index]
                cell = sheet.cell(row=row_num, column=col, value=total_val if total_val > 0 else "")
                cell.font = Font(name='Calibri', size=10, bold=True)
                cell.fill = PatternFill(start_color='E7E6E6', end_color='E7E6E6', fill_type='solid')
                cell.alignment = Alignment(horizontal='right', vertical='center')
                cell.number_format = '₹#,##0.00'
                cell.border = border
            else:
                cell = sheet.cell(row=row_num, column=col, value="")
                cell.border = border
    
    # Set column widths
    column_widths = [35, 15, 12, 12, 15, 10, 15, 15]
    for col, width in enumerate(column_widths, 1):
        sheet.column_dimensions[chr(64 + col)].width = width
    
    # Set row heights
    sheet.row_dimensions[7].height = 35  # Header row

if __name__ == "__main__":
    result = extract_exact_figures()
    if result:
        print(f"\n🎉 COMPLETED! Your file with exact figures is ready: {result}")
    else:
        print("\n❌ Process failed. Please check the error messages above.")
