import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import re

def process_all_sheets():
    """Process ALL sheets with exact figures"""
    input_file = 'Depreciation Workings (Actual work to be done).xlsx'
    output_file = 'COMPLETE_Depreciation_Schedule_All_Branches.xlsx'
    
    print("Processing ALL sheets with exact figures...")
    
    try:
        # Load workbook
        wb = openpyxl.load_workbook(input_file)
        print(f"Loaded workbook with {len(wb.sheetnames)} sheets")
        
        # Create new workbook
        new_wb = openpyxl.Workbook()
        new_wb.remove(new_wb.active)
        
        # Define styles
        title_font = Font(name='<PERSON><PERSON>ri', size=14, bold=True, color='FFFFFF')
        title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
        
        header_font = Font(name='<PERSON>ibri', size=11, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        
        subheader_font = Font(name='<PERSON><PERSON>ri', size=10, bold=True)
        subheader_fill = PatternFill(start_color='D9E2F3', end_color='D9E2F3', fill_type='solid')
        
        normal_font = Font(name='Calibri', size=9)
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Process ALL sheets
        successful_sheets = 0
        
        for i, sheet_name in enumerate(wb.sheetnames):
            print(f"Processing {i+1}/{len(wb.sheetnames)}: {sheet_name}")
            
            try:
                # Read sheet data
                df = pd.read_excel(input_file, sheet_name=sheet_name, header=None)
                
                # Skip very small sheets
                if df.shape[0] < 3 or df.shape[1] < 3:
                    print(f"  - Skipping {sheet_name} (too small)")
                    continue
                
                # Create new sheet with safe name
                safe_name = clean_sheet_name(sheet_name)
                new_sheet = new_wb.create_sheet(title=safe_name)
                
                # Extract asset data with exact figures
                asset_data = extract_asset_data_comprehensive(df, sheet_name)
                
                if asset_data and len(asset_data) > 0:
                    # Build the formatted sheet
                    build_comprehensive_sheet(new_sheet, sheet_name, asset_data, 
                                            title_font, title_fill, header_font, header_fill,
                                            subheader_font, subheader_fill, normal_font, border)
                    print(f"  ✓ Added {len(asset_data)} items with exact figures")
                    successful_sheets += 1
                else:
                    print(f"  - No data found in {sheet_name}")
                
            except Exception as e:
                print(f"  ✗ Error processing {sheet_name}: {str(e)}")
                continue
        
        # Save the file
        new_wb.save(output_file)
        print(f"\n🎉 SUCCESS: Complete file saved as: {output_file}")
        print(f"✓ Total sheets successfully processed: {successful_sheets}")
        return output_file
        
    except Exception as e:
        print(f"✗ ERROR: {str(e)}")
        return None

def clean_sheet_name(name):
    """Clean sheet name for Excel compatibility"""
    # Remove invalid characters and limit length
    clean_name = re.sub(r'[\\/*?:\[\]]', '_', name)
    return clean_name[:31]

def extract_asset_data_comprehensive(df, sheet_name):
    """Extract comprehensive asset data with exact figures"""
    items = []
    
    print(f"  Analyzing {df.shape[0]} rows x {df.shape[1]} columns")
    
    # Look for meaningful data rows
    for idx, row in df.iterrows():
        if idx > 150:  # Limit search to avoid timeout
            break
            
        row_values = [cell for cell in row]
        str_values = [str(cell) if pd.notna(cell) else "" for cell in row_values]
        
        # Skip empty rows
        if all(val == "" or val == "nan" for val in str_values):
            continue
            
        first_col = str_values[0].strip() if str_values else ""
        
        # Clean the name
        first_col = re.sub(r'[\r\n]+', ' ', first_col)
        first_col = re.sub(r'\s+', ' ', first_col)
        
        # Check if this is a meaningful data row
        if is_meaningful_data_row(first_col, str_values):
            # Extract all numeric values
            numeric_data = extract_all_numeric_values(row_values)
            
            if numeric_data and any(isinstance(val, (int, float)) and val != 0 for val in numeric_data):
                item_info = {
                    'name': first_col,
                    'values': numeric_data,
                    'row_index': idx
                }
                items.append(item_info)
    
    # Remove duplicates and limit
    unique_items = []
    seen_names = set()
    
    for item in items:
        name_key = item['name'].lower().strip()
        if (name_key not in seen_names and 
            len(unique_items) < 50 and  # Increased limit
            len(name_key) > 2):
            seen_names.add(name_key)
            unique_items.append(item)
    
    return unique_items

def is_meaningful_data_row(first_col, str_values):
    """Check if this row contains meaningful data"""
    if not first_col or len(first_col) < 3:
        return False
    
    # Skip obvious header/total rows
    skip_terms = ['nan', 'particulars', 'grand total', '__empty', 'trial balance',
                  'operations department', 'ashram branches', 'income & expenditure',
                  'balance sheet', 'liabilities', 'assets', 'amount', '`.']
    
    if any(term in first_col.lower() for term in skip_terms):
        return False
    
    # Must have at least one meaningful value in the row
    has_data = any(is_meaningful_value(val) for val in str_values[1:])
    
    return has_data

def is_meaningful_value(val):
    """Check if a value is meaningful (number or significant text)"""
    try:
        if val and str(val).strip() and str(val) != 'nan':
            # Try to convert to number
            clean_val = str(val).replace(',', '').replace('`', '').strip()
            if clean_val and clean_val != '.' and clean_val != '0':
                float(clean_val)
                return True
    except:
        # If not a number, check if it's meaningful text
        if val and len(str(val).strip()) > 2:
            return True
    return False

def extract_all_numeric_values(row_values):
    """Extract all numeric values from a row"""
    numeric_data = []
    
    for val in row_values[1:]:  # Skip first column
        try:
            if pd.notna(val) and str(val).strip() and str(val) != 'nan':
                clean_val = str(val).replace(',', '').replace('`', '').strip()
                
                if clean_val and clean_val != '.' and clean_val != '0':
                    try:
                        num_val = float(clean_val)
                        numeric_data.append(num_val)
                    except:
                        # If not a number, keep as text if meaningful
                        if len(clean_val) > 1:
                            numeric_data.append(clean_val)
                        else:
                            numeric_data.append("")
                else:
                    numeric_data.append("")
            else:
                numeric_data.append("")
        except:
            numeric_data.append("")
    
    return numeric_data

def build_comprehensive_sheet(sheet, sheet_name, items, title_font, title_fill, 
                             header_font, header_fill, subheader_font, subheader_fill, 
                             normal_font, border):
    """Build comprehensive formatted sheet"""
    
    # Title section
    sheet.merge_cells('A1:J1')
    sheet['A1'] = "SANTHIGIRI ASHRAM"
    sheet['A1'].font = title_font
    sheet['A1'].fill = title_fill
    sheet['A1'].alignment = Alignment(horizontal='center', vertical='center')
    
    sheet.merge_cells('A2:J2')
    sheet['A2'] = f"{sheet_name.upper()} - FINANCIAL DATA"
    sheet['A2'].font = header_font
    sheet['A2'].fill = header_fill
    sheet['A2'].alignment = Alignment(horizontal='center', vertical='center')
    
    sheet.merge_cells('A3:J3')
    sheet['A3'] = "FOR THE YEAR ENDED 31-03-2024"
    sheet['A3'].font = subheader_font
    sheet['A3'].fill = subheader_fill
    sheet['A3'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Empty row
    sheet.row_dimensions[4].height = 10
    
    # Table header
    sheet.merge_cells('A5:J5')
    sheet['A5'] = "DETAILED FINANCIAL SCHEDULE"
    sheet['A5'].font = subheader_font
    sheet['A5'].fill = subheader_fill
    sheet['A5'].alignment = Alignment(horizontal='center', vertical='center')
    
    # Column headers - flexible based on data
    headers = [
        "Description",
        "Value 1", 
        "Value 2",
        "Value 3",
        "Value 4",
        "Value 5",
        "Value 6",
        "Value 7",
        "Value 8",
        "Value 9"
    ]
    
    for col, header in enumerate(headers, 1):
        cell = sheet.cell(row=7, column=col, value=header)
        cell.font = Font(name='Calibri', size=9, bold=True)
        cell.fill = subheader_fill
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = border
    
    # Data rows
    row_num = 8
    
    for item in items:
        # Item name
        name_cell = sheet.cell(row=row_num, column=1, value=item['name'])
        name_cell.font = normal_font
        name_cell.alignment = Alignment(horizontal='left', vertical='center')
        name_cell.border = border
        
        # Item values
        values = item['values']
        for col in range(2, 11):  # Columns B through J
            data_index = col - 2
            if data_index < len(values):
                value = values[data_index]
                cell = sheet.cell(row=row_num, column=col, value=value)
                cell.font = normal_font
                cell.border = border
                
                if isinstance(value, (int, float)) and value != 0:
                    cell.alignment = Alignment(horizontal='right', vertical='center')
                    cell.number_format = '₹#,##0.00'
                else:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
            else:
                cell = sheet.cell(row=row_num, column=col, value="")
                cell.border = border
        
        row_num += 1
    
    # Set column widths
    column_widths = [40, 12, 12, 12, 12, 12, 12, 12, 12, 12]
    for col, width in enumerate(column_widths, 1):
        sheet.column_dimensions[chr(64 + col)].width = width
    
    # Set row heights
    sheet.row_dimensions[7].height = 25

if __name__ == "__main__":
    result = process_all_sheets()
    if result:
        print(f"\n🎉 FINAL SUCCESS! Complete file with all branches: {result}")
        print("This file contains exact figures from all sheets in the original file.")
    else:
        print("\n❌ Process failed. Please check the error messages above.")
