import openpyxl
import os

def verify_cleaned_files():
    """Verify the cleaned Excel files"""
    
    files_to_check = [
        'FINAL_Cleaned_Depreciation_Schedule.xlsx',
        'Cleaned_Depreciation_Schedule.xlsx'
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"\n=== CHECKING: {filename} ===")
            try:
                wb = openpyxl.load_workbook(filename)
                print(f"✓ File loaded successfully")
                print(f"✓ Number of sheets: {len(wb.sheetnames)}")
                print(f"✓ File size: {os.path.getsize(filename):,} bytes")
                
                # Check first few sheets
                for i, sheet_name in enumerate(wb.sheetnames[:3]):
                    sheet = wb[sheet_name]
                    print(f"  Sheet {i+1}: '{sheet_name}' - {sheet.max_row} rows x {sheet.max_column} cols")
                    
                    # Show first few cells
                    if sheet.max_row > 0:
                        for row in range(1, min(4, sheet.max_row + 1)):
                            cell_a = sheet.cell(row=row, column=1).value
                            if cell_a:
                                print(f"    Row {row}: {str(cell_a)[:50]}")
                
                print(f"✓ {filename} is valid and contains data")
                
            except Exception as e:
                print(f"✗ Error with {filename}: {e}")
        else:
            print(f"✗ File not found: {filename}")

if __name__ == "__main__":
    verify_cleaned_files()
